//! Terminal Panel for MATRIX IDE
//!
//! This module implements an integrated terminal panel that provides command execution
//! capabilities within the IDE environment.

use std::sync::Arc;
use std::process::{Command, St<PERSON>, Child};
use std::io::{<PERSON>ufReader, BufRead};
use std::thread;
use std::sync::Mutex;

use floem::{
    View, IntoView,
    views::{container, dyn_container, h_stack, h_stack_from_iter, label, v_stack, button, text_input, scroll, dyn_stack, Decorators},
    reactive::{create_rw_signal, RwSignal, SignalGet, SignalUpdate},
    keyboard::{Key, NamedKey},
};

use matrix_core::Engine as CoreEngine;
use crate::theme::ThemeManager;
use crate::error::UiError;
use super::{Panel, PanelConfig, PanelPosition, PanelState};

/// Terminal session state
#[derive(Clone)]
struct TerminalSession {
    /// Output lines
    output: RwSignal<Vec<String>>,
    /// Current input
    input: RwSignal<String>,
    /// Running process
    process: Arc<Mutex<Option<Child>>>,
    /// Working directory
    working_dir: RwSignal<String>,
    /// Session ID
    session_id: String,
}

impl TerminalSession {
    fn new(session_id: String) -> Self {
        let current_dir = std::env::current_dir()
            .map(|path| path.to_string_lossy().to_string())
            .unwrap_or_else(|_| ".".to_string());

        Self {
            output: create_rw_signal(vec![format!("Terminal Session {} - Directory: {}", session_id, current_dir)]),
            input: create_rw_signal(String::new()),
            process: Arc::new(Mutex::new(None)),
            working_dir: create_rw_signal(current_dir),
            session_id,
        }
    }
}

/// Terminal Panel that provides integrated command execution
#[derive(Clone)]
pub struct TerminalPanel {
    /// Core Engine
    core: Arc<CoreEngine>,
    /// Theme Manager
    theme_manager: Arc<ThemeManager>,
    /// Active terminal sessions
    sessions: RwSignal<Vec<TerminalSession>>,
    /// Currently active session index
    active_session: RwSignal<usize>,
    /// Panel configuration
    config: PanelConfig,
}

impl TerminalPanel {
    /// Create a new terminal panel
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        // Create initial terminal session
        let initial_session = TerminalSession::new("1".to_string());
        let sessions = create_rw_signal(vec![initial_session]);
        let active_session = create_rw_signal(0);

        let config = PanelConfig {
            id: "terminal".to_string(),
            position: PanelPosition::Bottom,
            state: PanelState::Visible,
            size: (300.0, 300.0),
            resizable: true,
            closable: true,
            movable: true,
            title: "Terminal".to_string(),
        };

        Ok(Self {
            core,
            theme_manager,
            sessions,
            active_session,
            config,
        })
    }

    /// Create the terminal panel view
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let sessions = self.sessions;
        let active_session = self.active_session;
        let self_clone = self.clone();

        container(
            v_stack((
                // Terminal header with tabs and controls
                self.create_header(),
                
                // Active terminal session view
                dyn_container(
                    move || (sessions.get(), active_session.get()),
                    move |(sessions_vec, active_idx)| {
                        if let Some(session) = sessions_vec.get(active_idx) {
                            self_clone.create_session_view(session).into_any()
                        } else {
                            label(|| "No active terminal session".to_string()).into_any()
                        }
                    }
                )
                .style(|s| s.flex_grow(1.0))
            ))
        )
        .style(move |s| {
            s.size_full()
             .background(theme.colors.background)
             .border_color(theme.colors.border)
        })
    }

    /// Create the terminal header with tabs and controls
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let sessions = self.sessions;
        let active_session = self.active_session;

        container(
            h_stack((
                // Terminal tabs
                dyn_container(
                    move || (sessions.get(), active_session.get()),
                    move |(sessions_vec, active_idx)| {
                        h_stack_from_iter(
                            sessions_vec.iter().enumerate().map(|(idx, session)| {
                                let is_active = idx == active_idx;
                                let session_id = session.session_id.clone();

                                button(label(move || format!("Terminal {}", session_id)))
                                    .action(move || {
                                        active_session.set(idx);
                                    })
                                    .style(move |s| {
                                        let mut style = s.padding(8.0).margin_right(4.0);
                                        if is_active {
                                            style = style.background(theme.colors.active);
                                        }
                                        style
                                    })
                            })
                        ).into_any()
                    }
                )
                .style(|s| s.flex_grow(1.0)),

                // Control buttons
                h_stack((
                    button(label(|| "+"))
                        .action({
                            let sessions = sessions;
                            move || {
                                sessions.update(|s| {
                                    let new_id = (s.len() + 1).to_string();
                                    s.push(TerminalSession::new(new_id));
                                });
                                active_session.set(sessions.get().len() - 1);
                            }
                        })
                        .style(|s| s.padding(4.0).margin_right(4.0)),
                        
                    button(label(|| "×"))
                        .action({
                            let sessions = sessions;
                            let active_session = active_session;
                            move || {
                                let active_idx = active_session.get();
                                sessions.update(|s| {
                                    if s.len() > 1 && active_idx < s.len() {
                                        s.remove(active_idx);
                                        if active_idx > 0 {
                                            active_session.set(active_idx - 1);
                                        }
                                    }
                                });
                            }
                        })
                        .style(|s| s.padding(4.0))
                ))
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .background(theme.colors.surface)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create view for a terminal session
    fn create_session_view(&self, session: &TerminalSession) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let output = session.output;
        let input = session.input;
        let working_dir = session.working_dir;
        let process = session.process.clone();

        v_stack((
            // Working directory display
            container(
                label(move || format!("Working Directory: {}", working_dir.get()))
                    .style(move |s| {
                        s.font_size(12.0)
                         .color(theme.colors.text_secondary)
                         .margin(4.0)
                    })
            ),

            // Terminal output area
            scroll(
                dyn_stack(
                    move || output.get(),
                    |line| line.clone(),
                    move |line| {
                        label(move || line.clone())
                            .style(move |s| {
                                s.width_full()
                                 .font_family("Fira Code, Consolas, monospace".to_string())
                                 .font_size(13.0)
                                 .color(theme.colors.text)
                                 .padding_vert(1.0)
                            })
                    }
                )
                .style(|s| s.width_full())
            )
            .style(|s| s.flex_grow(1.0).width_full()),

            // Command input area
            container(
                h_stack((
                    label(|| "> ")
                        .style(move |s| {
                            s.font_family("Fira Code, Consolas, monospace".to_string())
                             .color(theme.colors.text)
                        }),
                        
                    text_input(input)
                        .on_key_down(
                            Key::Named(NamedKey::Enter),
                            |_| true,
                            {
                                let output = output;
                                let input = input;
                                let process = process.clone();
                                let working_dir = working_dir;
                                move |_| {
                                    let command = input.get();
                                    if !command.trim().is_empty() {
                                        Self::execute_command(
                                            command,
                                            output,
                                            process.clone(),
                                            working_dir.get()
                                        );
                                        input.set(String::new());
                                    }
                                }
                            }
                        )
                        .style(move |s| {
                            s.flex_grow(1.0)
                             .padding(4.0)
                             .font_family("Fira Code, Consolas, monospace".to_string())
                             .background(theme.colors.surface)
                             .border(1.0)
                             .border_color(theme.colors.border)
                             .border_radius(4.0)
                        })
                ))
            )
            .style(move |s| {
                s.width_full()
                 .padding(8.0)
                 .background(theme.colors.surface)
                 .border_top(1.0)
                 .border_color(theme.colors.border)
            })
        ))
        .style(|s| s.size_full())
    }

    /// Execute a command in the terminal
    fn execute_command(
        cmd: String,
        output: RwSignal<Vec<String>>,
        process: Arc<Mutex<Option<Child>>>,
        working_dir: String
    ) {
        output.update(|o| o.push(format!("> {}", cmd)));

        // Handle built-in commands
        match cmd.trim() {
            "clear" | "cls" => {
                output.update(|o| o.clear());
                return;
            }
            cmd if cmd.starts_with("cd ") => {
                let path = cmd.strip_prefix("cd ").unwrap().trim();
                match std::env::set_current_dir(path) {
                    Ok(_) => {
                        output.update(|o| o.push(format!("Changed directory to: {}", path)));
                    }
                    Err(e) => {
                        output.update(|o| o.push(format!("cd: {}", e)));
                    }
                }
                return;
            }
            _ => {}
        }

        // Execute external command
        let output_clone = output;
        let working_dir_clone = working_dir;
        let cmd_clone = cmd.clone();

        thread::spawn(move || {
            let mut command = if cfg!(target_os = "windows") {
                let mut cmd = Command::new("cmd");
                cmd.args(["/C", &cmd_clone]);
                cmd
            } else {
                let mut cmd = Command::new("sh");
                cmd.args(["-c", &cmd_clone]);
                cmd
            };

            command.current_dir(&working_dir_clone)
                   .stdout(Stdio::piped())
                   .stderr(Stdio::piped());

            match command.spawn() {
                Ok(mut child) => {
                    if let Some(stdout) = child.stdout.take() {
                        let reader = BufReader::new(stdout);
                        for line in reader.lines() {
                            if let Ok(line) = line {
                                output_clone.update(|o| o.push(line));
                            }
                        }
                    }

                    match child.wait() {
                        Ok(status) => {
                            if !status.success() {
                                output_clone.update(|o| {
                                    o.push(format!("Command exited with code: {}", 
                                        status.code().unwrap_or(-1)))
                                });
                            }
                        }
                        Err(e) => {
                            output_clone.update(|o| o.push(format!("Error waiting for command: {}", e)));
                        }
                    }
                }
                Err(e) => {
                    output_clone.update(|o| o.push(format!("Failed to execute command: {}", e)));
                }
            }
        });
    }
}

impl Panel for TerminalPanel {
    fn id(&self) -> &str {
        "terminal"
    }

    fn title(&self) -> &str {
        &self.config.title
    }

    fn create_view(&self) -> Box<dyn View> {
        Box::new(self.create_view())
    }

    fn update(&mut self) -> Result<(), UiError> {
        // Update terminal state if needed
        Ok(())
    }
}
