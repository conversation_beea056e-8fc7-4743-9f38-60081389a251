//! Auto-completion System for Advanced Editor
//!
//! This module provides intelligent auto-completion capabilities
//! that integrate with LSP, syntax analysis, and AI suggestions.

use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::path::Path;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};

use crate::editor::lsp_integration::{LspClient, CompletionItem, CompletionItemKind};
use crate::editor::syntax_highlighting::{SyntaxHighlighter, TokenType};
use crate::error::UiError;
use matrix_core::Engine as CoreEngine;

/// Completion source types
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CompletionSource {
    /// Language Server Protocol
    Lsp,
    /// Syntax-based completion
    Syntax,
    /// AI-powered suggestions
    Ai,
    /// Snippet completion
    Snippet,
    /// File path completion
    FilePath,
    /// Word completion from current document
    Word,
}

/// Enhanced completion item with additional metadata
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EnhancedCompletionItem {
    /// Base completion item
    pub item: CompletionItem,
    /// Completion source
    pub source: CompletionSource,
    /// Relevance score (0.0 to 1.0)
    pub score: f32,
    /// Additional context information
    pub context: Option<String>,
    /// Whether this item is deprecated
    pub deprecated: bool,
    /// Commit characters that trigger completion
    pub commit_characters: Vec<String>,
    /// Additional text edits
    pub additional_text_edits: Vec<TextEdit>,
}

/// Text edit for completion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextEdit {
    /// Start position (line, column)
    pub start: (usize, usize),
    /// End position (line, column)
    pub end: (usize, usize),
    /// New text to insert
    pub new_text: String,
}

/// Code snippet template
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeSnippet {
    /// Snippet name
    pub name: String,
    /// Trigger prefix
    pub prefix: String,
    /// Snippet body with placeholders
    pub body: Vec<String>,
    /// Description
    pub description: String,
    /// Language scope
    pub scope: Vec<String>,
}

/// Completion context
#[derive(Debug, Clone)]
pub struct CompletionContext {
    /// File path
    pub file_path: std::path::PathBuf,
    /// Current line content
    pub line_content: String,
    /// Cursor position in line
    pub cursor_position: usize,
    /// Line number (0-based)
    pub line_number: usize,
    /// Surrounding context (previous/next lines)
    pub surrounding_context: Vec<String>,
    /// Current word being typed
    pub current_word: String,
    /// Language identifier
    pub language: String,
}

/// Auto-completion engine
pub struct AutoCompletionEngine {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// LSP client for language server completions
    lsp_client: Arc<LspClient>,
    
    /// Syntax highlighter for syntax-based completions
    syntax_highlighter: Arc<RwLock<SyntaxHighlighter>>,
    
    /// Code snippets by language
    snippets: Arc<RwLock<HashMap<String, Vec<CodeSnippet>>>>,
    
    /// Word cache for word-based completion
    word_cache: Arc<RwLock<HashMap<String, HashSet<String>>>>,
    
    /// Completion settings
    settings: CompletionSettings,
}

/// Completion engine settings
#[derive(Debug, Clone)]
pub struct CompletionSettings {
    /// Enable LSP completions
    pub enable_lsp: bool,
    /// Enable syntax completions
    pub enable_syntax: bool,
    /// Enable AI completions
    pub enable_ai: bool,
    /// Enable snippet completions
    pub enable_snippets: bool,
    /// Enable file path completions
    pub enable_file_paths: bool,
    /// Enable word completions
    pub enable_words: bool,
    /// Minimum word length for completion
    pub min_word_length: usize,
    /// Maximum number of completions to show
    pub max_completions: usize,
    /// Auto-trigger completion delay (ms)
    pub trigger_delay: u64,
    /// Case-sensitive matching
    pub case_sensitive: bool,
}

impl Default for CompletionSettings {
    fn default() -> Self {
        Self {
            enable_lsp: true,
            enable_syntax: true,
            enable_ai: true,
            enable_snippets: true,
            enable_file_paths: true,
            enable_words: true,
            min_word_length: 2,
            max_completions: 50,
            trigger_delay: 300,
            case_sensitive: false,
        }
    }
}

impl AutoCompletionEngine {
    /// Create a new auto-completion engine
    pub fn new(
        core: Arc<CoreEngine>,
        lsp_client: Arc<LspClient>,
        syntax_highlighter: Arc<RwLock<SyntaxHighlighter>>,
    ) -> Self {
        let mut engine = Self {
            core,
            lsp_client,
            syntax_highlighter,
            snippets: Arc::new(RwLock::new(HashMap::new())),
            word_cache: Arc::new(RwLock::new(HashMap::new())),
            settings: CompletionSettings::default(),
        };
        
        // Load default snippets synchronously using try_write
        if let Ok(mut snippets_guard) = engine.snippets.try_write() {
            Self::load_default_snippets(&mut snippets_guard);
        }
        
        engine
    }

    /// Get completions for a given context
    pub async fn get_completions(&self, context: CompletionContext) -> Result<Vec<EnhancedCompletionItem>, UiError> {
        let mut all_completions = Vec::new();

        // Get LSP completions
        if self.settings.enable_lsp {
            if let Ok(lsp_completions) = self.lsp_client.get_completion(
                &context.file_path,
                context.line_number,
                context.cursor_position,
            ).await {
                for item in lsp_completions {
                    all_completions.push(EnhancedCompletionItem {
                        item,
                        source: CompletionSource::Lsp,
                        score: 0.9, // High score for LSP completions
                        context: None,
                        deprecated: false,
                        commit_characters: vec![],
                        additional_text_edits: vec![],
                    });
                }
            }
        }

        // Get syntax-based completions
        if self.settings.enable_syntax {
            let syntax_completions = self.get_syntax_completions(&context).await?;
            all_completions.extend(syntax_completions);
        }

        // Get snippet completions
        if self.settings.enable_snippets {
            let snippet_completions = self.get_snippet_completions(&context).await?;
            all_completions.extend(snippet_completions);
        }

        // Get word completions
        if self.settings.enable_words {
            let word_completions = self.get_word_completions(&context).await?;
            all_completions.extend(word_completions);
        }

        // Get file path completions
        if self.settings.enable_file_paths && self.is_file_path_context(&context) {
            let file_path_completions = self.get_file_path_completions(&context).await?;
            all_completions.extend(file_path_completions);
        }

        // Filter and sort completions
        let filtered_completions = self.filter_and_sort_completions(all_completions, &context);

        Ok(filtered_completions)
    }

    /// Get syntax-based completions
    async fn get_syntax_completions(&self, context: &CompletionContext) -> Result<Vec<EnhancedCompletionItem>, UiError> {
        let mut completions = Vec::new();
        
        // Get language-specific keywords and built-ins
        let syntax_highlighter = self.syntax_highlighter.read().await;
        
        // For now, provide basic keyword completions
        let keywords = match context.language.as_str() {
            "rust" => vec!["fn", "let", "mut", "const", "if", "else", "match", "for", "while", "struct", "enum", "impl", "trait"],
            "javascript" | "typescript" => vec!["function", "const", "let", "var", "if", "else", "for", "while", "class", "interface"],
            "python" => vec!["def", "class", "if", "else", "elif", "for", "while", "import", "from", "try", "except"],
            _ => vec![],
        };

        for keyword in keywords {
            if keyword.starts_with(&context.current_word) {
                completions.push(EnhancedCompletionItem {
                    item: CompletionItem {
                        label: keyword.to_string(),
                        kind: Some(CompletionItemKind::Keyword),
                        detail: Some("keyword".to_string()),
                        documentation: None,
                        insert_text: Some(keyword.to_string()),
                        sort_text: None,
                        filter_text: None,
                    },
                    source: CompletionSource::Syntax,
                    score: 0.7,
                    context: None,
                    deprecated: false,
                    commit_characters: vec![" ".to_string()],
                    additional_text_edits: vec![],
                });
            }
        }

        Ok(completions)
    }

    /// Get snippet completions
    async fn get_snippet_completions(&self, context: &CompletionContext) -> Result<Vec<EnhancedCompletionItem>, UiError> {
        let mut completions = Vec::new();
        let snippets = self.snippets.read().await;

        if let Some(language_snippets) = snippets.get(&context.language) {
            for snippet in language_snippets {
                if snippet.prefix.starts_with(&context.current_word) {
                    completions.push(EnhancedCompletionItem {
                        item: CompletionItem {
                            label: snippet.prefix.clone(),
                            kind: Some(CompletionItemKind::Snippet),
                            detail: Some(snippet.name.clone()),
                            documentation: Some(snippet.description.clone()),
                            insert_text: Some(snippet.body.join("\n")),
                            sort_text: None,
                            filter_text: None,
                        },
                        source: CompletionSource::Snippet,
                        score: 0.8,
                        context: None,
                        deprecated: false,
                        commit_characters: vec![],
                        additional_text_edits: vec![],
                    });
                }
            }
        }

        Ok(completions)
    }

    /// Get word completions from document
    async fn get_word_completions(&self, context: &CompletionContext) -> Result<Vec<EnhancedCompletionItem>, UiError> {
        let mut completions = Vec::new();
        let word_cache = self.word_cache.read().await;

        if let Some(words) = word_cache.get(&context.file_path.to_string_lossy().to_string()) {
            for word in words {
                if word.len() >= self.settings.min_word_length 
                    && word != &context.current_word
                    && word.starts_with(&context.current_word) {
                    completions.push(EnhancedCompletionItem {
                        item: CompletionItem {
                            label: word.clone(),
                            kind: Some(CompletionItemKind::Text),
                            detail: Some("word".to_string()),
                            documentation: None,
                            insert_text: Some(word.clone()),
                            sort_text: None,
                            filter_text: None,
                        },
                        source: CompletionSource::Word,
                        score: 0.5,
                        context: None,
                        deprecated: false,
                        commit_characters: vec![],
                        additional_text_edits: vec![],
                    });
                }
            }
        }

        Ok(completions)
    }

    /// Get file path completions
    async fn get_file_path_completions(&self, context: &CompletionContext) -> Result<Vec<EnhancedCompletionItem>, UiError> {
        let mut completions = Vec::new();

        // Extract partial path from current word
        let partial_path = &context.current_word;
        let base_dir = if partial_path.starts_with('/') {
            std::path::PathBuf::from("/")
        } else {
            context.file_path.parent().unwrap_or(Path::new(".")).to_path_buf()
        };

        // Read directory entries
        if let Ok(entries) = tokio::fs::read_dir(&base_dir).await {
            let mut entries = entries;
            while let Ok(Some(entry)) = entries.next_entry().await {
                if let Ok(file_name) = entry.file_name().into_string() {
                    if file_name.starts_with(partial_path) {
                        let is_dir = entry.file_type().await.map(|ft| ft.is_dir()).unwrap_or(false);
                        
                        completions.push(EnhancedCompletionItem {
                            item: CompletionItem {
                                label: file_name.clone(),
                                kind: Some(if is_dir { CompletionItemKind::Folder } else { CompletionItemKind::File }),
                                detail: Some(if is_dir { "directory" } else { "file" }.to_string()),
                                documentation: None,
                                insert_text: Some(file_name),
                                sort_text: None,
                                filter_text: None,
                            },
                            source: CompletionSource::FilePath,
                            score: 0.6,
                            context: None,
                            deprecated: false,
                            commit_characters: vec![],
                            additional_text_edits: vec![],
                        });
                    }
                }
            }
        }

        Ok(completions)
    }

    /// Check if current context suggests file path completion
    fn is_file_path_context(&self, context: &CompletionContext) -> bool {
        let line = &context.line_content;
        
        // Check for common file path patterns
        line.contains("import") || 
        line.contains("require") || 
        line.contains("include") || 
        line.contains("\"./") || 
        line.contains("'./") || 
        line.contains("\"/") || 
        line.contains("'/")
    }

    /// Filter and sort completions
    fn filter_and_sort_completions(&self, mut completions: Vec<EnhancedCompletionItem>, context: &CompletionContext) -> Vec<EnhancedCompletionItem> {
        // Filter by current word
        if !context.current_word.is_empty() {
            completions.retain(|item| {
                if self.settings.case_sensitive {
                    item.item.label.starts_with(&context.current_word)
                } else {
                    item.item.label.to_lowercase().starts_with(&context.current_word.to_lowercase())
                }
            });
        }

        // Sort by score (descending) and then by label
        completions.sort_by(|a, b| {
            b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal)
                .then_with(|| a.item.label.cmp(&b.item.label))
        });

        // Limit number of completions
        completions.truncate(self.settings.max_completions);

        completions
    }

    /// Update word cache for a document
    pub async fn update_word_cache(&self, file_path: &Path, content: &str) {
        let mut words = HashSet::new();
        
        // Extract words from content
        for word in content.split_whitespace() {
            let cleaned_word = word.trim_matches(|c: char| !c.is_alphanumeric() && c != '_');
            if cleaned_word.len() >= self.settings.min_word_length {
                words.insert(cleaned_word.to_string());
            }
        }

        self.word_cache.write().await.insert(file_path.to_string_lossy().to_string(), words);
    }

    /// Load default code snippets
    fn load_default_snippets(snippets: &mut HashMap<String, Vec<CodeSnippet>>) {
        // Rust snippets
        let rust_snippets = vec![
            CodeSnippet {
                name: "Function".to_string(),
                prefix: "fn".to_string(),
                body: vec!["fn ${1:name}(${2:params}) -> ${3:ReturnType} {", "    ${4:// body}", "}"].into_iter().map(String::from).collect(),
                description: "Function definition".to_string(),
                scope: vec!["rust".to_string()],
            },
            CodeSnippet {
                name: "Struct".to_string(),
                prefix: "struct".to_string(),
                body: vec!["struct ${1:Name} {", "    ${2:field}: ${3:Type},", "}"].into_iter().map(String::from).collect(),
                description: "Struct definition".to_string(),
                scope: vec!["rust".to_string()],
            },
            CodeSnippet {
                name: "Impl".to_string(),
                prefix: "impl".to_string(),
                body: vec!["impl ${1:Type} {", "    ${2:// methods}", "}"].into_iter().map(String::from).collect(),
                description: "Implementation block".to_string(),
                scope: vec!["rust".to_string()],
            },
        ];
        snippets.insert("rust".to_string(), rust_snippets);

        // JavaScript snippets
        let js_snippets = vec![
            CodeSnippet {
                name: "Function".to_string(),
                prefix: "function".to_string(),
                body: vec!["function ${1:name}(${2:params}) {", "    ${3:// body}", "}"].into_iter().map(String::from).collect(),
                description: "Function declaration".to_string(),
                scope: vec!["javascript".to_string(), "typescript".to_string()],
            },
            CodeSnippet {
                name: "Arrow Function".to_string(),
                prefix: "arrow".to_string(),
                body: vec!["const ${1:name} = (${2:params}) => {", "    ${3:// body}", "};"].into_iter().map(String::from).collect(),
                description: "Arrow function".to_string(),
                scope: vec!["javascript".to_string(), "typescript".to_string()],
            },
        ];
        snippets.insert("javascript".to_string(), js_snippets.clone());
        snippets.insert("typescript".to_string(), js_snippets);

        // Python snippets
        let python_snippets = vec![
            CodeSnippet {
                name: "Function".to_string(),
                prefix: "def".to_string(),
                body: vec!["def ${1:name}(${2:params}):", "    ${3:pass}"].into_iter().map(String::from).collect(),
                description: "Function definition".to_string(),
                scope: vec!["python".to_string()],
            },
            CodeSnippet {
                name: "Class".to_string(),
                prefix: "class".to_string(),
                body: vec!["class ${1:Name}:", "    def __init__(self${2:, params}):", "        ${3:pass}"].into_iter().map(String::from).collect(),
                description: "Class definition".to_string(),
                scope: vec!["python".to_string()],
            },
        ];
        snippets.insert("python".to_string(), python_snippets);
    }

    /// Update completion settings
    pub fn update_settings(&mut self, settings: CompletionSettings) {
        self.settings = settings;
    }

    /// Get current settings
    pub fn get_settings(&self) -> &CompletionSettings {
        &self.settings
    }
}
